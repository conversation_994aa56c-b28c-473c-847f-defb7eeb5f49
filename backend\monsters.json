[{"id": "小混混", "name": "小混混", "hp": 100, "max_hp": 100, "attack": 10, "defense": 10, "agility": 30, "description": "街头的小混混，欺软怕硬，实力一般。", "attack_descriptions": ["{attacker}挥舞着拳头，朝{defender}的脸上砸去，造成{damage}点伤害！", "{attacker}偷偷踢了{defender}一脚，{defender}吃痛不已（-{damage}）！", "{attacker}大喊一声，扑向{defender}，拳头直奔胸口！", "{attacker}趁{defender}不备，狠狠推了一把！", "{attacker}抓起地上的石头扔向{defender}，砸中了！", "{attacker}用肘击猛击{defender}的肚子！", "{attacker}恶狠狠地扯住{defender}的衣领，试图将其摔倒！", "{attacker}朝{defender}的膝盖踢了一脚！", "{attacker}吐了口唾沫，骂骂咧咧地冲上来攻击！"], "rewards": {"money": 1, "experience": 5, "skill_points": 1}, "attack_mode": "active", "active_attacks": [{"description": "{attacker}用头撞向{defender}，{defender}感到一阵中毒！", "chance": 0.2, "special_effect": "中毒"}], "drops": [{"item_id": "ore_coal", "chance": 0.12, "quantity_min": 1, "quantity_max": 2, "description": "小混混身上的煤炭"}, {"item_id": "herb_wildgrass", "chance": 0.08, "quantity_min": 1, "quantity_max": 1, "description": "野草掉落"}]}, {"id": "混混头目", "name": "混混头目", "hp": 150, "max_hp": 150, "attack": 10, "defense": 10, "agility": 50, "description": "小混混们的头目，稍有些本事。", "attack_descriptions": ["{attacker}挥舞着棍棒，狠狠砸向{defender}的肩膀！", "{attacker}冷笑一声，飞起一脚踢向{defender}！", "{attacker}用力推搡{defender}，让其踉跄后退！", "{attacker}大声呵斥，趁机给了{defender}一拳！", "{attacker}用膝盖顶向{defender}的腹部！", "{attacker}抓住{defender}的手臂，试图将其扭伤！", "{attacker}用棍棒敲打{defender}的腿部！", "{attacker}突然冲刺，撞翻了{defender}！", "{attacker}用力甩了{defender}一个耳光！", "{attacker}怒吼着扑向{defender}，造成{damage}点伤害！"], "rewards": {"experience": 10, "skill_points": 2}, "attack_mode": "active", "active_attacks": [{"description": "{attacker}用头狠狠地撞向{defender}，{defender}感到一阵中毒！", "chance": 0.2, "special_effect": "中毒"}], "drops": [{"item_id": "heal_potion", "chance": 0.15, "quantity_min": 1, "quantity_max": 1, "description": "混混头目的疗伤药"}, {"item_id": "wood_firewood", "chance": 0.1, "quantity_min": 1, "quantity_max": 3, "description": "柴火掉落"}, {"item_id": "chitie_jian", "chance": 0.05, "quantity_min": 1, "quantity_max": 1, "description": "赤铁剑掉落"}]}, {"id": "流浪汉", "name": "流浪汉", "hp": 200, "max_hp": 200, "attack": 10, "defense": 10, "agility": 35, "description": "流浪街头，偶尔会发疯攻击路人。", "attack_descriptions": ["{attacker}挥舞着脏兮兮的手臂，胡乱拍打{defender}！", "{attacker}突然扑向{defender}，咬了一口！", "{attacker}用破布条抽打{defender}的脸！", "{attacker}发出怪叫，朝{defender}扔出垃圾！", "{attacker}用力推了{defender}一把！", "{attacker}抓住{defender}的头发，试图拉倒对方！", "{attacker}用膝盖顶向{defender}的腰部！", "{attacker}用力拍打{defender}的背部！", "{attacker}突然抓住{defender}的胳膊不放！", "{attacker}发疯似地乱踢乱打，造成{damage}点伤害！"], "rewards": {"experience": 4, "skill_points": 1}, "attack_mode": "passive", "active_attacks": [{"description": "{attacker}用脏兮兮的头撞向{defender}，{defender}感到一阵中毒！", "chance": 0.2, "special_effect": "中毒"}], "drops": [{"item_id": "herb_wildgrass", "chance": 0.2, "quantity_min": 1, "quantity_max": 2, "description": "流浪汉身上的野草"}, {"item_id": "fur_feather", "chance": 0.15, "quantity_min": 1, "quantity_max": 1, "description": "落羽掉落"}]}, {"id": "小女孩", "name": "小女孩", "hp": 50, "max_hp": 50, "attack": 5, "defense": 5, "agility": 30, "description": "看似无害的小女孩，偶尔会恶作剧。", "attack_descriptions": ["{attacker}突然拧了{defender}一下，{defender}吃痛！", "{attacker}用力踩了{defender}的脚！", "{attacker}朝{defender}扔了一颗小石子！", "{attacker}拉扯{defender}的衣角，试图让其摔倒！", "{attacker}用手指戳了戳{defender}的脸！", "{attacker}突然大叫一声，吓了{defender}一跳！", "{attacker}用力推了{defender}一下！", "{attacker}偷偷在{defender}背后拍了一下！", "{attacker}用小拳头锤了{defender}一下！", "{attacker}恶作剧地扔出泥巴，砸中了{defender}！"], "rewards": {"experience": 1, "skill_points": 1}, "attack_mode": "passive", "drops": [{"item_id": "herb_wildgrass", "chance": 0.25, "quantity_min": 1, "quantity_max": 1, "description": "小女孩的野草"}]}, {"id": "小男孩", "name": "小男孩", "hp": 50, "max_hp": 50, "attack": 5, "defense": 5, "agility": 20, "description": "调皮的小男孩，喜欢恶作剧。", "attack_descriptions": ["{attacker}用弹弓射向{defender}，弹丸击中目标！", "{attacker}突然踢了{defender}一脚！", "{attacker}用力拉扯{defender}的头发！", "{attacker}朝{defender}扔出泥巴！", "{attacker}用小拳头锤了{defender}的背！", "{attacker}偷偷在{defender}背后拍了一下！", "{attacker}用力推了{defender}一下！", "{attacker}突然大叫一声，吓了{defender}一跳！", "{attacker}用力踩了{defender}的脚！", "{attacker}恶作剧地扔出小石子，砸中了{defender}！"], "rewards": {"experience": 1, "skill_points": 1}, "attack_mode": "passive", "drops": [{"item_id": "ore_coal", "chance": 0.18, "quantity_min": 1, "quantity_max": 1, "description": "小男孩的煤炭"}, {"item_id": "wood_firewood", "chance": 0.12, "quantity_min": 1, "quantity_max": 2, "description": "柴火掉落"}]}, {"id": "老鸨", "name": "老鸨", "hp": 150, "max_hp": 150, "attack": 15, "defense": 15, "agility": 20, "description": "青楼老鸨，嘴巴厉害，偶尔会动手。", "attack_descriptions": ["{attacker}挥舞着手中的拂尘，抽打{defender}！", "{attacker}用力扇了{defender}一个耳光！", "{attacker}大声呵斥，吓得{defender}一愣！", "{attacker}用拐杖敲打{defender}的腿！", "{attacker}用力推了{defender}一把！", "{attacker}用手指戳了戳{defender}的额头！", "{attacker}突然拧了{defender}一下！", "{attacker}用力踩了{defender}的脚！", "{attacker}用拂尘扫了{defender}一脸灰！", "{attacker}恶狠狠地骂了{defender}一顿！"], "rewards": {"experience": 6, "skill_points": 2}, "attack_mode": "passive", "drops": [{"item_id": "heal_potion", "chance": 0.2, "quantity_min": 1, "quantity_max": 2, "description": "老鸨的疗伤药"}, {"item_id": "lucky_coin", "chance": 0.08, "quantity_min": 1, "quantity_max": 1, "description": "幸运金币掉落"}]}, {"id": "地痞", "name": "地痞", "hp": 200, "max_hp": 200, "attack": 30, "defense": 20, "agility": 25, "description": "地头蛇，欺压百姓，手段狠辣。", "attack_descriptions": ["{attacker}挥舞着铁棍，朝{defender}砸去！", "{attacker}用力踢了{defender}一脚！", "{attacker}用肩膀猛撞{defender}！", "{attacker}大声咆哮，吓得{defender}一愣！", "{attacker}用铁棍敲打{defender}的手臂！", "{attacker}用力推了{defender}一把！", "{attacker}用膝盖顶向{defender}的腹部！", "{attacker}用力甩了{defender}一个耳光！", "{attacker}突然冲刺，撞翻了{defender}！", "{attacker}怒吼着扑向{defender}，造成{damage}点伤害！"], "rewards": {"experience": 7, "skill_points": 2}, "attack_mode": "active", "active_attacks": [{"description": "{attacker}突然转身用头狠狠地撞向{defender}，{defender}感到一阵中毒！", "chance": 0.2, "special_effect": "中毒"}], "drops": [{"item_id": "chitie_jian", "chance": 0.12, "quantity_min": 1, "quantity_max": 1, "description": "地痞的赤铁剑"}, {"item_id": "heal_potion", "chance": 0.18, "quantity_min": 1, "quantity_max": 2, "description": "疗伤药掉落"}, {"item_id": "ore_coal", "chance": 0.15, "quantity_min": 2, "quantity_max": 4, "description": "煤炭掉落"}]}, {"id": "苦行僧", "name": "苦行僧", "hp": 300, "max_hp": 300, "attack": 60, "defense": 40, "agility": 35, "description": "修行中的苦行僧，偶尔会考验路人。", "attack_descriptions": ["{attacker}挥舞着禅杖，敲打{defender}的肩膀！", "{attacker}口诵佛号，突然一掌拍向{defender}！", "{attacker}用力推了{defender}一把！", "{attacker}用禅杖点向{defender}的膝盖！", "{attacker}用手指戳了戳{defender}的额头！", "{attacker}突然拧了{defender}一下！", "{attacker}用力踩了{defender}的脚！", "{attacker}用禅杖扫了{defender}一腿！", "{attacker}大声呵斥，吓得{defender}一愣！", "{attacker}口中念念有词，突然发力攻击！"], "rewards": {"experience": 8, "skill_points": 2}, "attack_mode": "passive", "active_attacks": [{"description": "{attacker}突然掏出一根禅杖猛地砸向{defender}的头部，{defender}感到一阵天旋地转！", "chance": 0.2, "special_effect": "中毒"}], "drops": [{"item_id": "heal_potion", "chance": 0.25, "quantity_min": 1, "quantity_max": 3, "description": "苦行僧的疗伤药"}, {"item_id": "mystery_box", "chance": 0.05, "quantity_min": 1, "quantity_max": 1, "description": "神秘宝箱掉落"}, {"item_id": "herb_wildgrass", "chance": 0.3, "quantity_min": 2, "quantity_max": 5, "description": "野草掉落"}]}, {"id": "山贼", "name": "山贼", "hp": 400, "max_hp": 400, "attack": 100, "defense": 60, "agility": 40, "description": "山林中的山贼，凶狠狡诈。", "attack_descriptions": ["{attacker}挥舞着大刀，朝{defender}砍去！", "{attacker}用力踢了{defender}一脚！", "{attacker}用肩膀猛撞{defender}！", "{attacker}大声咆哮，吓得{defender}一愣！", "{attacker}用大刀敲打{defender}的手臂！", "{attacker}用力推了{defender}一把！", "{attacker}用膝盖顶向{defender}的腹部！", "{attacker}用力甩了{defender}一个耳光！", "{attacker}突然冲刺，撞翻了{defender}！", "{attacker}怒吼着扑向{defender}，造成{damage}点伤害！"], "rewards": {"experience": 10, "skill_points": 3}, "attack_mode": "active"}]