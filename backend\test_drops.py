#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试怪物掉落系统
"""

import json
import random
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_monsters():
    """加载怪物配置"""
    try:
        with open('monsters.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载怪物配置失败: {e}")
        return []

def test_monster_drops(monster, test_count=100):
    """测试单个怪物的掉落"""
    print(f"\n=== 测试怪物: {monster['name']} ===")
    
    drops = monster.get('drops', [])
    if not drops:
        print("该怪物没有配置掉落物品")
        return
    
    print("掉落配置:")
    for drop in drops:
        print(f"  - {drop['item_id']}: 概率{drop['chance']*100:.1f}%, 数量{drop['quantity_min']}-{drop['quantity_max']}")
    
    print(f"\n进行{test_count}次掉落测试:")
    
    # 统计掉落结果
    drop_stats = {}
    total_drops = 0
    
    for i in range(test_count):
        battle_drops = []
        
        # 模拟掉落计算
        for drop in drops:
            if random.random() < drop.get('chance', 0.1):
                quantity_min = drop.get('quantity_min', 1)
                quantity_max = drop.get('quantity_max', 1)
                quantity = random.randint(quantity_min, quantity_max)
                
                battle_drops.append({
                    'id': drop['item_id'],
                    'quantity': quantity
                })
        
        # 统计结果
        for item in battle_drops:
            item_id = item['id']
            quantity = item['quantity']
            
            if item_id not in drop_stats:
                drop_stats[item_id] = {'count': 0, 'total_quantity': 0}
            
            drop_stats[item_id]['count'] += 1
            drop_stats[item_id]['total_quantity'] += quantity
            total_drops += 1
    
    # 输出统计结果
    print("\n掉落统计结果:")
    for item_id, stats in drop_stats.items():
        drop_rate = (stats['count'] / test_count) * 100
        avg_quantity = stats['total_quantity'] / stats['count'] if stats['count'] > 0 else 0
        print(f"  - {item_id}: 掉落{stats['count']}次 ({drop_rate:.1f}%), 总数量{stats['total_quantity']}, 平均数量{avg_quantity:.1f}")
    
    print(f"\n总掉落次数: {total_drops}, 平均每次战斗掉落: {total_drops/test_count:.2f}个物品")

def main():
    """主函数"""
    print("怪物掉落系统测试")
    print("=" * 50)
    
    monsters = load_monsters()
    if not monsters:
        print("没有找到怪物配置")
        return
    
    # 测试所有有掉落配置的怪物
    tested_count = 0
    for monster in monsters:
        if monster.get('drops'):
            test_monster_drops(monster, 1000)  # 每个怪物测试1000次
            tested_count += 1
    
    if tested_count == 0:
        print("没有找到配置了掉落的怪物")
    else:
        print(f"\n测试完成！共测试了{tested_count}个怪物的掉落系统")

if __name__ == "__main__":
    main()
