# 战斗系统
# 负责处理所有战斗相关的逻辑，包括回合制战斗、伤害计算、状态效果等

import asyncio
import json
import random
from enhanced_martial_system import get_martial_move, get_martial_info, get_martial_category, get_attack_description, format_battle_description
import unicodedata

def get_equipped_martial(player):
    weapon_type = None
    weapon = player.get('equipped_weapon')
    if weapon:
        weapon_type = weapon.get('type') or weapon.get('类型')
    skills = player.get('martial_skills', [])
    # 先找与主手武器类型匹配且已装备的武功
    if isinstance(skills, list):
        for skill in skills:
            if skill.get('equipped') and weapon_type and (skill.get('type') == weapon_type or skill.get('类型') == weapon_type):
                return skill.get('name')
        # 没有武器或没有匹配武功时，优先空手类
        for skill in skills:
            if skill.get('equipped') and (skill.get('type') == '空手' or skill.get('类型') == '空手'):
                return skill.get('name')
        # 否则返回第一个已装备的
        for skill in skills:
            if skill.get('equipped'):
                return skill.get('name')
    elif isinstance(skills, dict):
        for k, v in skills.items():
            if v.get('equipped') and weapon_type and (v.get('type') == weapon_type or v.get('类型') == weapon_type):
                return k
        for k, v in skills.items():
            if v.get('equipped') and (v.get('type') == '空手' or v.get('类型') == '空手'):
                return k
        for k, v in skills.items():
            if v.get('equipped'):
                return k
    return None

async def simulate_battle_stepwise(player: dict, enemy: dict, websocket, user_id: str = None) -> dict:
    """
    分步模拟战斗，每回合推送战斗信息到前端
    每回合推送'battle_round'，战斗结束推送'battle_result'
    """
    import random
    from enhanced_martial_system import get_martial_move, get_martial_info, get_martial_category, get_attack_description, format_battle_description
    
    player_hp = player['hp']
    enemy_hp = enemy.get('hp', 100)  # 支持怪物自定义血量
    player_name = player.get('name', '玩家')
    enemy_name = enemy.get('name', enemy.get('id', '敌人'))
    
    # 玩家武功（如有装备武学，否则用基础拳法）
    player_martial = player.get('equipped_martial', '基本拳法')
    
    # 敌人武功（如为人类且有martials则随机选一个，否则用基础拳法）
    enemy_martial = '基本拳法'
    if enemy.get('type') == '人类' and enemy.get('martials'):
        martials = [m.split(':')[0] for m in enemy['martials'].split(',') if m]
        if martials:
            enemy_martial = random.choice(martials)
    
    # 初始化状态效果
    if 'status_effects' not in player:
        player['status_effects'] = []
    if 'status_effects' not in enemy:
        enemy['status_effects'] = []
    
    # 获取怪物攻击模式
    is_active_monster = enemy.get('attack_mode') == 'active'
    enemy_active_attacks = enemy.get('active_attacks', [])
    enemy_attack_descs = enemy.get('attack_descriptions', [])
    
    battle_log = []
    battle_rewards = {}  # 初始化战斗奖励
    round_num = 1

    # 设置玩家战斗状态，防止自动恢复系统干扰
    original_status = player.get('status', 'normal')
    player['status'] = 'battle'
    print(f"[战斗系统] 设置玩家状态为战斗状态，原状态: {original_status}")

    # 计算战斗奖励
    def calculate_battle_rewards():
        """计算战斗胜利奖励"""
        rewards = {}

        # 从怪物配置的rewards字段获取奖励
        monster_rewards = enemy.get('rewards', {})
        print(f"[战斗系统] 怪物配置奖励: {monster_rewards}")

        # 历练值奖励（对应配置中的experience字段）
        base_experience = monster_rewards.get('experience', 10)
        rewards['历练值'] = base_experience

        # 银两奖励（对应配置中的money字段）
        base_money = monster_rewards.get('money', random.randint(5, 15))
        rewards['银两'] = base_money

        # 武学点奖励（对应配置中的skill_points字段）
        base_skill_points = monster_rewards.get('skill_points', random.randint(1, 3))
        rewards['武学点'] = base_skill_points

        # 物品掉落（基于新的drops配置）
        drops = enemy.get('drops', [])
        if drops:
            dropped_items = []
            for drop in drops:
                # 检查掉落概率
                if random.random() < drop.get('chance', 0.1):
                    # 计算掉落数量
                    quantity_min = drop.get('quantity_min', 1)
                    quantity_max = drop.get('quantity_max', 1)
                    quantity = random.randint(quantity_min, quantity_max)

                    dropped_items.append({
                        'id': drop['item_id'],
                        'quantity': quantity,
                        'description': drop.get('description', f"{drop['item_id']}掉落")
                    })
                    print(f"[战斗系统] 掉落物品: {drop['item_id']} x{quantity} (概率: {drop.get('chance', 0.1)})")

            if dropped_items:
                rewards['物品'] = dropped_items

        return rewards
    
    # 修改do_attack签名，增加effect_descs参数，默认空
    async def do_attack(attacker, is_extra_attack=False, effect_descs=None):
        nonlocal player_hp, enemy_hp, round_num
        nonlocal player, enemy, player_name, enemy_name, player_martial, enemy_martial
        nonlocal battle_log
        import random
        if attacker == 'player':
            martial = None  # 保证后续if martial:安全
            move_name = ''  # 保证日志安全
            stunned = False
            for eff in player['status_effects'][:]:
                if eff['type'] == '眩晕' and eff['turns'] > 0:
                    stunned = True
                    eff['turns'] -= 1
                    effect_descs.append(f"{player_name}被眩晕，无法行动！")
                    if eff['turns'] <= 0:
                        player['status_effects'].remove(eff)
                    break
            if stunned:
                msg = {
                    'type': 'battle_round',
                    'data': {
                        'round': round_num,
                        'attacker': player_name,
                        'defender': enemy_name,
                        'damage': 0,
                        'desc': f"{player_name}被眩晕，无法行动！",
                        'move': move_name,
                        'martial': '',
                        'player_hp': max(0, player_hp),
                        'enemy_hp': max(0, enemy_hp),
                        'enemy_max_hp': enemy.get('max_hp', 100),
                        'player_mp': player.get('mp', 0),
                        'player_max_mp': player.get('max_mp', 0),
                        'special_effect': '眩晕',
                        'effect_desc': '\n'.join(effect_descs) if effect_descs else ''
                    }
                }
                await websocket.send(json.dumps(msg, ensure_ascii=False))
                battle_log.append(msg['data'])
                await asyncio.sleep(1)
                return
            # 命中-闪避判定
            attacker_agility = player.get('agility', 10)
            defender_agility = enemy.get('agility', 10)
            final_hit_rate = calc_hit_rate(attacker_agility, defender_agility)
            if player_hp > 0 and enemy_hp > 0 and random.random() < final_hit_rate:
                # 破防特效：不减去防御
                special_effect = None
                martial_info = get_martial_info(player_martial)
                if martial_info and 'special_effect' in martial_info:
                    special_effect = martial_info['special_effect']
                # 破防特效
                if special_effect == '破防':
                    base_damage = max(1, int(player.get('attack', 10) * 0.8))
                    variation = random.uniform(0.85, 1.15)
                    damage = max(1, int(base_damage * variation))
                    enemy_hp -= damage  # 确保先扣血
                    desc = f"{player_name}突然福至心灵，出招神鬼莫测直接无视了{enemy_name}的防御，造成{damage}点致命伤害！"
                else:
                    base_damage = max(1, int(player.get('attack', 10) * 0.8 - enemy.get('defense', 10) if isinstance(enemy, dict) else 10))
                    variation = random.uniform(0.85, 1.15)
                    damage = max(1, int(base_damage * variation))
                    enemy_hp -= damage  # 确保先扣血
                    # 生成描述
                    if martial:
                        move_name, move_desc = get_martial_move(martial, player_martial_skills)
                    else:
                        move_desc = get_attack_description(player)
                    try:
                        move_desc = str(move_desc).format(attacker=player_name, defender=enemy_name, damage=damage, move=move_name)
                    except Exception as e:
                        print("move_desc format error:", move_desc, move_name, e)
                        move_desc = f"{player_name}攻击{enemy_name}，造成{damage}点伤害！"
                    # 玩家攻击desc拼接
                    if ('点伤害' in move_desc or '点轻伤' in move_desc):
                        desc = move_desc
                    else:
                        desc = move_desc
                        if damage > 0:
                            desc += f"（造成{int(damage)}点伤害）"
                martial = player_martial
                weapon = player.get('equipped_weapon')
                martial_type = get_martial_category(martial) if martial else None
                def normalize_name(name):
                    return unicodedata.normalize('NFKC', name.strip()) if isinstance(name, str) else name
                norm_martial = normalize_name(martial)
                player_martial_skills = player.get('martial_skills')
                if isinstance(player_martial_skills, dict):
                    player_martial_skills = {normalize_name(k): v for k, v in player_martial_skills.items()}
                elif isinstance(player_martial_skills, list):
                    for skill in player_martial_skills:
                        if 'name' in skill:
                            skill['name'] = normalize_name(skill['name'])
                move_name = '普通攻击'
                move_desc = ''
                martial = get_equipped_martial(player)
                player_martial_skills = player.get('martial_skills', [])
                if not martial:
                    weapon_type = None
                    weapon = player.get('equipped_weapon')
                    if weapon:
                        weapon_type = weapon.get('type') or weapon.get('类型')
                    basic_martial = None
                    for skill in player_martial_skills:
                        if skill.get('name', '').startswith('基本') and (skill.get('type') == weapon_type or skill.get('类型') == weapon_type) and skill.get('unlocked'):
                            basic_martial = skill.get('name')
                            break
                    if not basic_martial:
                        for skill in player_martial_skills:
                            if skill.get('name', '').startswith('基本') and (skill.get('type') == '空手' or skill.get('类型') == '空手') and skill.get('unlocked'):
                                basic_martial = skill.get('name')
                                break
                    martial = basic_martial
                if martial:
                    move_name, move_desc = get_martial_move(martial, player_martial_skills)
                else:
                    move_desc = get_attack_description(player)
                try:
                    move_desc = str(move_desc).format(attacker=player_name, defender=enemy_name, damage=damage, move=move_name)
                except Exception as e:
                    print("move_desc format error:", move_desc, move_name, e)
                    move_desc = f"{player_name}攻击{enemy_name}，造成{damage}点伤害！"
                # 玩家攻击desc拼接
                if ('点伤害' in move_desc or '点轻伤' in move_desc):
                    desc = move_desc
                else:
                    desc = f"{move_desc}（造成{damage}点伤害）"
                msg = {
                    'type': 'battle_round',
                    'data': {
                        'round': round_num,
                        'attacker': player_name,
                        'defender': enemy_name,
                        'damage': damage,
                        'desc': desc,
                        'move': move_name,
                        'martial': norm_martial,
                        'player_hp': max(0, player_hp),
                        'enemy_hp': max(0, enemy_hp),
                        'enemy_max_hp': enemy.get('max_hp', 100),
                        'player_mp': player.get('mp', 0),
                        'player_max_mp': player.get('max_mp', 0),
                        'special_effect': special_effect,
                        'effect_desc': '\n'.join(effect_descs) if effect_descs else ''
                    }
                }
                await websocket.send(json.dumps(msg, ensure_ascii=False))
                battle_log.append(msg['data'])
                await asyncio.sleep(1)
                # 反伤特效：敌方有反伤时反弹伤害
                if damage > 0 and ('反伤' in (enemy.get('special_effects', []) if isinstance(enemy, dict) else []) or (isinstance(enemy, dict) and enemy.get('special_effect') == '反伤')):
                    reflect_damage = int(enemy.get('max_hp', 100) * 0.05)
                    player_hp -= reflect_damage
                    reflect_msg = {
                        'type': 'battle_round',
                        'data': {
                            'round': round_num,
                            'attacker': enemy_name,
                            'defender': player_name,
                            'damage': reflect_damage,
                            'desc': f"{enemy_name}反弹了{player_name}的攻击，造成{reflect_damage}点反伤！",
                            'move': '',
                            'martial': '',
                            'player_hp': max(0, player_hp),
                            'enemy_hp': max(0, enemy_hp),
                            'enemy_max_hp': enemy.get('max_hp', 100),
                            'player_mp': player.get('mp', 0),
                            'player_max_mp': player.get('max_mp', 0),
                            'special_effect': '反伤',
                            'effect_desc': ''
                        }
                    }
                    await websocket.send(json.dumps(reflect_msg, ensure_ascii=False))
                    battle_log.append(reflect_msg['data'])
                    await asyncio.sleep(1)
                # 连击特效：额外攻击一次
                if special_effect == '连击' and not is_extra_attack:
                    await do_attack('player', is_extra_attack=True)
            else:
                msg = {
                    'type': 'battle_round',
                    'data': {
                        'round': round_num,
                        'attacker': player_name,
                        'defender': enemy_name,
                        'damage': 0,
                        'desc': f"{enemy_name}微微一笑闪开了{player_name}的攻击！",
                        'move': move_name,
                        'martial': '',
                        'player_hp': max(0, player_hp),
                        'enemy_hp': max(0, enemy_hp),
                        'enemy_max_hp': enemy.get('max_hp', 100),
                        'player_mp': player.get('mp', 0),
                        'player_max_mp': player.get('max_mp', 0),
                        'special_effect': '闪避',
                        'effect_desc': '\n'.join(effect_descs) if effect_descs else ''
                    }
                }
                await websocket.send(json.dumps(msg, ensure_ascii=False))
                battle_log.append(msg['data'])
                await asyncio.sleep(1)
        elif attacker == 'enemy':
            martial = None  # 保证后续if martial:安全
            move_name = ''  # 保证日志安全
            stunned = False
            for eff in enemy['status_effects'][:]:
                if eff['type'] == '眩晕' and eff['turns'] > 0:
                    stunned = True
                    eff['turns'] -= 1
                    effect_descs.append(f"{enemy_name}被眩晕，无法行动！")
                    if eff['turns'] <= 0:
                        enemy['status_effects'].remove(eff)
                    break
            if stunned:
                msg = {
                    'type': 'battle_round',
                    'data': {
                        'round': round_num,
                        'attacker': enemy_name,
                        'defender': player_name,
                        'damage': 0,
                        'desc': f"{enemy_name}被眩晕，无法行动！",
                        'move': move_name,
                        'martial': '',
                        'player_hp': max(0, player_hp),
                        'enemy_hp': max(0, enemy_hp),
                        'enemy_max_hp': enemy.get('max_hp', 100),
                        'special_effect': '眩晕',
                        'effect_desc': '\n'.join(effect_descs) if effect_descs else ''
                    }
                }
                await websocket.send(json.dumps(msg, ensure_ascii=False))
                battle_log.append(msg['data'])
                await asyncio.sleep(1)
                return
            attacker_agility = enemy.get('agility', 10)
            defender_agility = player.get('agility', 10)
            final_hit_rate = calc_hit_rate(attacker_agility, defender_agility)
            if enemy_hp > 0 and player_hp > 0 and random.random() < final_hit_rate:
                special_effect = None
                martial_info = get_martial_info(enemy_martial)
                if martial_info and 'special_effect' in martial_info:
                    special_effect = martial_info['special_effect']
                desc = ''
                # 怪物主动攻击（active_attacks）
                if enemy_active_attacks and isinstance(enemy_active_attacks, list):
                    for skill in enemy_active_attacks:
                        if random.random() < skill.get('chance', 0):
                            desc = skill['description'].format(attacker=enemy_name, defender=player_name, damage=0)
                            special_effect = skill.get('special_effect')
                            # 特效处理
                            if special_effect == '中毒':
                                player['status_effects'].append({'type': '中毒', 'turns': 3, 'value': max(1, int(player.get('max_hp', 100) * 0.025))})
                                effect_descs.append(f"{player_name}中毒了，未来3回合每回合损失{max(1, int(player.get('max_hp', 100) * 0.025))}点生命！")
                            if special_effect == '流血':
                                player['status_effects'].append({'type': '流血', 'turns': 2, 'value': 8})
                                effect_descs.append(f"{player_name}流血不止，未来2回合每回合损失8点生命！")
                            if special_effect == '眩晕':
                                player['status_effects'].append({'type': '眩晕', 'turns': 1, 'value': 0})
                                effect_descs.append(f"{player_name}被眩晕，下一回合无法行动！")
                            if special_effect == '连击' and not is_extra_attack:
                                await do_attack('enemy', is_extra_attack=True)
                            if special_effect == '破防':
                                enemy_damage = max(1, int(round(enemy.get('attack', 15) * 0.6 if isinstance(enemy, dict) else 9)))
                                player_hp -= enemy_damage
                                desc = f"{enemy_name}突然福至心灵，出招神鬼莫测直接无视了{player_name}的防御，造成{enemy_damage}点致命伤害！"
                            else:
                                enemy_damage = max(1, int(round((enemy.get('attack', 15) * 0.6 if isinstance(enemy, dict) else 9) - player.get('defense', 5))))
                                player_hp -= enemy_damage
                            break
                # 怪物普通攻击
                if not desc:
                    base_enemy_damage = max(1, int(enemy.get('attack', 15) * 0.6 - player.get('defense', 5)))
                    variation = random.uniform(0.85, 1.15)
                    enemy_damage = max(1, int(base_enemy_damage * variation))  # 只用整数
                    player_hp -= enemy_damage
                    if enemy_attack_descs and isinstance(enemy_attack_descs, list) and len(enemy_attack_descs) > 0:
                        desc = random.choice(enemy_attack_descs).format(attacker=enemy_name, defender=player_name, damage=enemy_damage)
                        move_name = ''
                    else:
                        enemy_martial = enemy.get('skills', ['基本拳法'])[0] if isinstance(enemy, dict) and enemy.get('skills') else '基本拳法'
                        move_name, move_desc = get_martial_move(enemy_martial)
                        move_desc = move_desc.format(attacker=enemy_name, defender=player_name, damage=enemy_damage)
                        desc = format_battle_description('hit', enemy_name, player_name, move_name, move_desc, enemy_damage)
                    # 怪物攻击desc拼接
                    if enemy_damage > 0 and ('点伤害' not in desc):
                        desc += f"（造成{int(enemy_damage)}点伤害）"
                # battle_round日志
                msg = {
                    'type': 'battle_round',
                    'data': {
                        'round': round_num,
                        'attacker': enemy_name,
                        'defender': player_name,
                        'damage': enemy_damage,
                        'desc': desc,
                        'move': move_name,
                        'martial': '',
                        'player_hp': max(0, player_hp),
                        'enemy_hp': max(0, enemy_hp),
                        'enemy_max_hp': enemy.get('max_hp', 100),
                        'special_effect': special_effect,
                        'effect_desc': '\n'.join(effect_descs) if effect_descs else ''
                    }
                }
                await websocket.send(json.dumps(msg, ensure_ascii=False))
                battle_log.append(msg['data'])
                await asyncio.sleep(1)
                # 反伤特效：玩家有反伤时反弹伤害
                if enemy_damage > 0 and ('反伤' in (player.get('special_effects', []) if isinstance(player, dict) else []) or (isinstance(player, dict) and player.get('special_effect') == '反伤')):
                    reflect_damage = int(player.get('max_hp', 100) * 0.05)
                    enemy_hp -= reflect_damage
                    reflect_msg = {
                        'type': 'battle_round',
                        'data': {
                            'round': round_num,
                            'attacker': player_name,
                            'defender': enemy_name,
                            'damage': reflect_damage,
                            'desc': f"{player_name}反弹了{enemy_name}的攻击，造成{reflect_damage}点反伤！",
                            'move': '',
                            'martial': '',
                            'player_hp': max(0, player_hp),
                            'enemy_hp': max(0, enemy_hp),
                            'enemy_max_hp': enemy.get('max_hp', 100),
                            'special_effect': '反伤',
                            'effect_desc': ''
                        }
                    }
                    await websocket.send(json.dumps(reflect_msg, ensure_ascii=False))
                    battle_log.append(reflect_msg['data'])
                    await asyncio.sleep(1)
                # 连击特效：额外攻击一次
                if special_effect == '连击' and not is_extra_attack:
                    await do_attack('enemy', is_extra_attack=True)
            else:
                msg = {
                    'type': 'battle_round',
                    'data': {
                        'round': round_num,
                        'attacker': enemy_name,
                        'defender': player_name,
                        'damage': 0,
                        'desc': f"{player_name}微微一笑闪开了{enemy_name}的攻击！",
                        'move': move_name,
                        'martial': '',
                        'player_hp': max(0, player_hp),
                        'enemy_hp': max(0, enemy_hp),
                        'enemy_max_hp': enemy.get('max_hp', 100),
                        'special_effect': '闪避',
                        'effect_desc': '\n'.join(effect_descs) if effect_descs else ''
                    }
                }
                await websocket.send(json.dumps(msg, ensure_ascii=False))
                battle_log.append(msg['data'])
                await asyncio.sleep(1)
    
        # 检查玩家是否已经逃跑成功
        if hasattr(websocket, '_escaped') and websocket._escaped:
            print(f"[战斗系统] 检测到玩家已逃跑，停止战斗")
            # 恢复玩家气血到战斗前的状态
            player['hp'] = player.get('hp', 100)
            # 推送逃跑成功的玩家数据
            await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            return {'win': False, 'escaped': True, 'player_hp': player['hp']}
        
        # 处理状态效果
        effect_descs = []
        
        # 处理玩家状态效果
        for eff in player['status_effects'][:]:
            if eff['type'] == '中毒':
                damage = eff['value']
                player_hp -= damage
                effect_descs.append(f"{player_name}受到中毒伤害，损失{damage}点生命！")
            elif eff['type'] == '流血':
                damage = eff['value']
                player_hp -= damage
                effect_descs.append(f"{player_name}流血不止，损失{damage}点生命！")
            eff['turns'] -= 1
            if eff['turns'] <= 0:
                player['status_effects'].remove(eff)
        
        # 处理敌人状态效果
        for eff in enemy['status_effects'][:]:
            if eff['type'] == '中毒':
                damage = eff['value']
                enemy_hp -= damage
                effect_descs.append(f"{enemy_name}受到中毒伤害，损失{damage}点生命！")
            elif eff['type'] == '流血':
                damage = eff['value']
                enemy_hp -= damage
                effect_descs.append(f"{enemy_name}流血不止，损失{damage}点生命！")
            eff['turns'] -= 1
            if eff['turns'] <= 0:
                enemy['status_effects'].remove(eff)
        
    max_rounds = 20
    battle_escaped = False
    while player_hp > 0 and enemy_hp > 0:
        # 检查是否已经逃跑
        if hasattr(websocket, '_escaped') and websocket._escaped:
            print(f"[战斗系统] 战斗结束时检测到玩家已逃跑")
            player['hp'] = max(0, player_hp)
            # 恢复玩家原始状态
            player['status'] = original_status
            print(f"[战斗系统] 玩家逃跑，恢复玩家状态为: {original_status}")
            await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            battle_escaped = True
            break
        # 最大回合数限制
        if round_num > max_rounds:
            print(f"[战斗系统] 达到最大回合数限制({max_rounds}回合)，战斗结束")
            msg = {
                'type': 'battle_round',
                'data': {
                    'round': round_num,
                    'attacker': '',
                    'defender': '',
                    'damage': 0,
                    'desc': f'【第{round_num}回合】你与{enemy_name}激战{max_rounds}回合，体力不支，被迫撤退，战斗失败！',
                    'move': '',
                    'martial': '',
                    'player_hp': max(0, player_hp),
                    'enemy_hp': max(0, enemy_hp),
                    'enemy_max_hp': enemy.get('max_hp', 100),
                    'special_effect': '',
                    'effect_desc': f'战斗已进行{max_rounds}回合，达到最大回合数限制'
                }
            }
            await websocket.send(json.dumps(msg, ensure_ascii=False))
            battle_log.append(msg['data'])
            await asyncio.sleep(1)
            result = {
                'win': False,
                'player_hp': max(0, player_hp),
                'enemy_hp': max(0, enemy_hp),
                'battle_log': battle_log,
                'rewards': {}
            }
            # 恢复玩家原始状态
            player['status'] = original_status
            print(f"[战斗系统] 达到最大回合数，恢复玩家状态为: {original_status}")
            await websocket.send(json.dumps({'type': 'battle_result', 'data': result}, ensure_ascii=False))
            await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            return result
        # 每回合初始化effect_descs
        effect_descs = []
        # 处理玩家状态效果
        for eff in player['status_effects'][:]:
            if eff['type'] == '中毒':
                damage = eff['value']
                player_hp -= damage
                effect_descs.append(f"{player_name}受到中毒伤害，损失{damage}点生命！")
            elif eff['type'] == '流血':
                damage = eff['value']
                player_hp -= damage
                effect_descs.append(f"{player_name}流血不止，损失{damage}点生命！")
            eff['turns'] -= 1
            if eff['turns'] <= 0:
                player['status_effects'].remove(eff)
        # 处理敌人状态效果
        for eff in enemy['status_effects'][:]:
            if eff['type'] == '中毒':
                damage = eff['value']
                enemy_hp -= damage
                effect_descs.append(f"{enemy_name}受到中毒伤害，损失{damage}点生命！")
            elif eff['type'] == '流血':
                damage = eff['value']
                enemy_hp -= damage
                effect_descs.append(f"{enemy_name}流血不止，损失{damage}点生命！")
            eff['turns'] -= 1
            if eff['turns'] <= 0:
                enemy['status_effects'].remove(eff)

        # 决定攻击顺序：主动攻击型怪物永远先出手
        if is_active_monster:
            attack_order = ['enemy', 'player']
        else:
            attack_order = ['player', 'enemy']

        print(f"[DEBUG] 回合{round_num}，攻击顺序: {attack_order}")
        for attacker in attack_order:
            print(f"[DEBUG] 本回合执行攻击方: {attacker}")
            await do_attack(attacker, effect_descs=effect_descs)
            # 每次攻击后同步血量到主数据，防止血量乱跳
            old_player_hp = player.get('hp', 0)
            old_enemy_hp = enemy.get('hp', 0) if isinstance(enemy, dict) else 0
            player['hp'] = max(0, player_hp)
            if isinstance(enemy, dict):
                enemy['hp'] = max(0, enemy_hp)
            print(f"[战斗系统] 血量同步: 玩家 {old_player_hp}->{player['hp']}, 敌人 {old_enemy_hp}->{enemy.get('hp', 0) if isinstance(enemy, dict) else enemy_hp}")
            # 注意：不在这里推送player_data，避免与battle_round冲突
        round_num += 1
    # 循环结束后，推送最终战斗结果（未逃跑才推送）
    if not battle_escaped:
        print("[DEBUG] 推送 battle_result")
        # 战斗失败时强制保留1点血量
        if player_hp <= 0:
            player_hp = 1
            player['hp'] = 1
        else:
            player['hp'] = max(0, player_hp)
        player['hp'] = max(0, player_hp)
        enemy['hp'] = max(0, enemy_hp) if isinstance(enemy, dict) else enemy_hp
        
        # 计算战斗奖励
        if player_hp > 0 and enemy_hp <= 0:
            battle_rewards = calculate_battle_rewards()
            print(f"[战斗系统] 战斗胜利，计算奖励: {battle_rewards}")

            # 应用奖励到玩家
            if '历练值' in battle_rewards:
                old_exp = player.get('experience', 0)
                player['experience'] = old_exp + battle_rewards['历练值']
                print(f"[战斗系统] 历练值奖励: {old_exp} + {battle_rewards['历练值']} = {player['experience']}")

            if '银两' in battle_rewards:
                old_money = player.get('money', 0)
                player['money'] = old_money + battle_rewards['银两']
                print(f"[战斗系统] 银两奖励: {old_money} + {battle_rewards['银两']} = {player['money']}")

            if '武学点' in battle_rewards:
                old_skill_points = player.get('skill_points', 0)
                player['skill_points'] = old_skill_points + battle_rewards['武学点']
                print(f"[战斗系统] 武学点奖励: {old_skill_points} + {battle_rewards['武学点']} = {player['skill_points']}")

            if '物品' in battle_rewards:
                print(f"[战斗系统] 物品奖励: {battle_rewards['物品']}")
                # 通过物品系统添加物品到背包
                from inventory_system import add_item_to_inventory
                for item in battle_rewards['物品']:
                    item_id = item['id']
                    quantity = item.get('quantity', 1)
                    try:
                        # 异步添加物品到背包
                        success = await add_item_to_inventory(
                            player,
                            {'id': item_id, 'quantity': quantity},
                            user_id=user_id,
                            save_func=None  # 这里不保存，由上层统一保存
                        )
                        if success:
                            print(f"[战斗系统] 成功添加物品到背包: {item_id} x{quantity}")
                        else:
                            print(f"[战斗系统] 添加物品到背包失败: {item_id} x{quantity}")
                    except Exception as e:
                        print(f"[战斗系统] 添加物品到背包异常: {item_id} x{quantity}, 错误: {e}")

        result = {
            'win': player_hp > 0 and enemy_hp <= 0,
            'player_hp': max(0, player_hp),
            'enemy_hp': max(0, enemy_hp),
            'battle_log': battle_log,
            'rewards': battle_rewards if player_hp > 0 and enemy_hp <= 0 else {}
        }

        # 更新玩家血量
        player['hp'] = max(0, player_hp)

        # 恢复玩家原始状态
        player['status'] = original_status
        print(f"[战斗系统] 战斗结束，恢复玩家状态为: {original_status}")

        await websocket.send(json.dumps({'type': 'battle_result', 'data': result}, ensure_ascii=False))
        await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
        return result
    else:
        # 逃跑时也要恢复玩家状态
        player['status'] = original_status
        print(f"[战斗系统] 玩家逃跑，恢复玩家状态为: {original_status}")
        return {'win': False, 'escaped': True, 'player_hp': player['hp']}


def calc_hit_rate(attacker_agility, defender_agility):
    diff = attacker_agility - defender_agility
    base_hit = 1.0
    if diff >= 0:
        hit_rate = base_hit
    else:
        hit_rate = base_hit + 0.03 * diff
    hit_rate = max(0.2, min(1.0, hit_rate))
    return hit_rate


def simulate_battle(player: dict, enemy: dict) -> dict:
    """模拟战斗，支持敌人武功出招描述"""
    player_hp = player['hp']
    enemy_hp = enemy.get('hp', 100)  # 支持怪物自定义血量
    player_name = player.get('name', '玩家')
    enemy_name = enemy.get('name', enemy.get('id', '敌人'))
    # 玩家武功（如有装备武学，否则用基础拳法）
    player_martial = player.get('equipped_martial', '基本拳法')
    # 敌人武功（如为人类且有martials则随机选一个，否则用基础拳法）
    enemy_martial = '基本拳法'
    if enemy.get('type') == '人类' and enemy.get('martials'):
        martials = [m.split(':')[0] for m in enemy['martials'].split(',') if m]
        if martials:
            enemy_martial = random.choice(martials)
    rounds = []
    while player_hp > 0 and enemy_hp > 0:
        # 玩家攻击
        if random.random() < player.get('hit_rate', 0.8):
            damage = max(1, player.get('attack', 10) - enemy.get('defense', 10))
            enemy_hp -= damage
            # 武功描述
            move_name, move_desc = get_martial_move(player_martial)
            desc = format_battle_description(move_name, move_desc, player_name, enemy_name, damage)
            desc += f"（造成{damage}伤害）"
            rounds.append({'attacker': player_name, 'defender': enemy_name, 'damage': damage, 'desc': desc, 'move': move_name, 'martial': player_martial, 'player_hp': max(0, player_hp), 'enemy_hp': max(0, enemy_hp)})
        # 敌人攻击
        if enemy_hp > 0 and random.random() < 0.8:
            enemy_damage = max(1, enemy.get('attack', 15) - player.get('defense', 5))
            player_hp -= enemy_damage
            # 武功描述
            move_name, move_desc = get_martial_move(enemy_martial)
            move_desc = move_desc.format(attacker=enemy_name, defender=player_name, damage=enemy_damage)
            desc = format_battle_description(move_name, move_desc, enemy_name, player_name, enemy_damage)
            desc += f"（造成{enemy_damage}伤害）"
            rounds.append({'attacker': enemy_name, 'defender': player_name, 'damage': enemy_damage, 'desc': desc, 'move': move_name, 'martial': enemy_martial, 'player_hp': max(0, player_hp), 'enemy_hp': max(0, enemy_hp)})
    return {
        'win': player_hp > 0,
        'player_hp': max(0, player_hp),
        'enemy_hp': max(0, enemy_hp),
        'battle_log': rounds
    } 

