/**
 * 游戏状态管理器
 * 负责管理前端游戏状态，与WebSocket通信配合
 */

import wsManager from './websocket.js'

class GameStateManager {
	constructor() {
		// 游戏数据（从服务器获取）
		this.player = null
		this.inventory = []
		this.equipment = {}
		this.skills = []
		this.eventLog = []
		this.money = 0
		this.gold = 0
		this.status = 'normal'
		// 新增：物品配置缓存
		this.itemsConfig = {} 
		// 新增：地图配置缓存
		this.mapsConfig = {} 
		// 状态更新回调
		this.updateCallbacks = []
		// 新增认证标志
		this.isAuthed = false
		// 初始化WebSocket事件监听
		this.initWebSocketHandlers()
	}

	/**
	 * 初始化WebSocket事件处理器
	 */
	initWebSocketHandlers() {
		
		// 玩家数据更新
		wsManager.on('player_data', (data) => {
			if (data && typeof data === 'object') {
				if (data.data && typeof data.data === 'object') {
					// 处理 {data: {...}} 格式
					this.player = data.data;
				} else {
					// 处理直接返回对象的格式
					this.player = data;
				}
				this.notifyUpdate('player');
			} else {
				uni.showToast({
					title: '玩家数据格式错误',
					icon: 'none'
				})
			}
		});

		// 背包数据更新
		wsManager.on('inventory_data', (data) => {
			// 后端返回的数据格式是 {inventory: [...], capacity: 50}
			// 我们需要提取inventory数组
			if (data && data.inventory && Array.isArray(data.inventory)) {
				this.inventory = data.inventory;
			} else if (Array.isArray(data)) {
				// 兼容直接返回数组的情况
				this.inventory = data;
			} else if (data && typeof data === 'object') {
				// 尝试提取data字段
				if (data.data && Array.isArray(data.data)) {
					this.inventory = data.data;
				} else {
					uni.showToast({
						title: '背包数据格式无法识别',
						icon: 'none'
					})
					this.inventory = [];
				}
			} else {
				uni.showToast({
					title: '背包数据格式错误',
					icon: 'none'
				})
				this.inventory = [];
			}
			this.notifyUpdate('inventory');
		});

		// 装备数据更新
		wsManager.on('equipment_data', (data) => {
			if (data && typeof data === 'object') {
				if (data.data && typeof data.data === 'object') {
					// 处理 {data: {...}} 格式
					this.equipment = data.data;
				} else {
					// 处理直接返回对象的格式
					this.equipment = data;
				}
			} else {
				uni.showToast({
					title: '装备数据格式错误',
					icon: 'none'
				})
				this.equipment = {};
			}
			this.notifyUpdate('equipment');
		});

		// 武功数据更新
		wsManager.on('skills_data', (data) => {
			if (data && Array.isArray(data)) {
				this.skills = data;
			} else if (data && typeof data === 'object' && data.data && Array.isArray(data.data)) {
				this.skills = data.data;
			} else {
				uni.showToast({
					title: '武功数据格式错误',
					icon: 'none'
				})
				this.skills = [];
			}
			this.notifyUpdate('skills');
		});

		// 事件日志更新
		wsManager.on('event_log', (data) => {
			if (data && Array.isArray(data)) {
				this.eventLog = data;
			} else if (data && typeof data === 'object' && data.data && Array.isArray(data.data)) {
				this.eventLog = data.data;
			} else {
				uni.showToast({
					title: '事件日志格式错误',
					icon: 'none'
				})
				this.eventLog = [];
			}
			this.notifyUpdate('eventLog');
		});

		// 货币更新
		wsManager.on('currency_update', (data) => {
			if (data && typeof data === 'object') {
				this.money = data.silver || data.money || this.money || 0;
				this.gold = data.gold || this.gold || 0;
				this.notifyUpdate('currency');
			} else {
				uni.showToast({
					title: '货币数据格式错误',
					icon: 'none'
				})
			}
		});

		// 状态更新
		wsManager.on('status_update', (data) => {
			if (data && typeof data === 'object') {
				this.status = data.status || this.status || 'normal';
				this.notifyUpdate('status');
			} else {
				uni.showToast({
					title: '状态数据格式错误',
					icon: 'none'
				})
			}
		});

		// 体力值更新
		wsManager.on('energy_update', (data) => {
			if (data && typeof data === 'object' && this.player) {
				this.player.energy = data.energy || this.player.energy || 0;
				this.player.max_energy = data.max_energy || this.player.max_energy || 0;
				this.player.energy_regen_rate = data.energy_regen_rate || this.player.energy_regen_rate || 0;
				// 接收后端计算的体力恢复详情
				this.player.energy_regen_details = data.energy_regen_details || null;
				this.notifyUpdate('player');
			} else {
				uni.showToast({
					title: '体力数据格式错误或玩家数据不存在',
					icon: 'none'
				})
			}
		});

		// 认证成功
		wsManager.on('auth_success', (data) => {
			this.isAuthed = true;
			
			// 处理认证成功时返回的玩家数据
			if (data && typeof data === 'object') {
				if (data.player && typeof data.player === 'object') {
					this.player = data.player;
				}
				
				if (data.userInfo && typeof data.userInfo === 'object') {
					// 如果认证响应中包含userInfo，也尝试更新玩家数据
					if (!this.player) this.player = {};
					
					// 合并userInfo中的关键字段到player
					const userInfo = data.userInfo;
					this.player.username = userInfo.username || this.player.username;
					this.player.characterName = userInfo.characterName || this.player.characterName;
					this.player.userId = userInfo.userId || this.player.userId;
					this.player.gender = userInfo.gender || this.player.gender;
					
					// 如果有talent数据，也更新
					if (userInfo.talent && typeof userInfo.talent === 'object') {
						this.player.talent = userInfo.talent;
					}
					
				}
				
				// 处理货币数据
				if (data.money !== undefined) this.money = data.money;
				if (data.silver !== undefined) this.money = data.silver;
				if (data.gold !== undefined) this.gold = data.gold;
			}
			
			// 通知所有关键状态更新
			this.notifyUpdate('player');
			this.notifyUpdate('currency');
			this.notifyUpdate('status');
			this.notifyUpdate('auth');
			
			// 认证成功后，主动请求完整的玩家数据
			setTimeout(() => {
				this.requestAllData();
			}, 500);
		});

		// 认证失败
		wsManager.on('auth_failed', (data) => {
			this.isAuthed = false;
			this.notifyUpdate('auth');
		});

		// 登录成功
		wsManager.on('login_success', (data) => {
			this.isAuthed = true;
			
			// 处理登录成功时返回的玩家数据
			if (data && typeof data === 'object') {
				if (data.player && typeof data.player === 'object') {
					this.player = data.player;
				}
				
				if (data.userInfo && typeof data.userInfo === 'object') {
					// 如果登录响应中包含userInfo，也尝试更新玩家数据
					if (!this.player) this.player = {};
					
					// 合并userInfo中的关键字段到player
					const userInfo = data.userInfo;
					this.player.username = userInfo.username || this.player.username;
					this.player.characterName = userInfo.characterName || this.player.characterName;
					this.player.userId = userInfo.userId || this.player.userId;
					this.player.gender = userInfo.gender || this.player.gender;
					
					// 如果有talent数据，也更新
					if (userInfo.talent && typeof userInfo.talent === 'object') {
						this.player.talent = userInfo.talent;
					}
					

				}
				
				// 处理货币数据
				if (data.money !== undefined) this.money = data.money;
				if (data.silver !== undefined) this.money = data.silver;
				if (data.gold !== undefined) this.gold = data.gold;
			}
			
			// 通知所有关键状态更新
			this.notifyUpdate('player');
			this.notifyUpdate('currency');
			this.notifyUpdate('status');
			this.notifyUpdate('auth');
			
			// 登录成功后，主动请求完整的玩家数据
			setTimeout(() => {
				this.requestAllData();
			}, 500);
		});

		// 错误消息
		wsManager.on('error', (data) => {
			// 过滤掉一些不需要显示的错误
			const message = data.message || '操作失败'

			// 跳过一些内部错误或调试信息
			if (message.includes('武功数据错误') ||
				message.includes('list indices must be integers') ||
				message.includes('dict object has no attribute') ||
				message.includes('str object has no attribute')) {
				console.error('内部错误（已过滤）:', message)
				return
			}

			uni.showToast({
				title: message,
				icon: 'none'
			})
		})

		// 成功消息
		wsManager.on('success', (data) => {
			uni.showToast({
				title: data.message || '操作成功',
				icon: 'success'
			})
		})
		
		// 监听连接状态变化
		wsManager.on('connected', () => {
			// WebSocket连接已建立
		})
		
		wsManager.on('disconnected', () => {
			// WebSocket连接已断开
		})
	}

	/**
	 * 处理游戏事件
	 */
	handleGameEvent(data) {
		
		// 兼容不同的数据格式
		const eventType = data.type || data.eventType || 5;
		const content = data.content || data.description || '你遇到了一个江湖事件';
		const rewards = data.rewards || {};
		const realm_breakthrough = data.realm_breakthrough || null;

		// 追加到事件日志
		const logEntry = {
			timestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
			name: this.getEventTypeName(eventType),
			description: content
		};

		this.eventLog.unshift(logEntry);

		// 最多保留50条
		if (this.eventLog.length > 50) this.eventLog = this.eventLog.slice(0, 50);

		// 通知更新
		this.notifyUpdate('eventLog');

		// 处理境界突破
		if (realm_breakthrough) {
			setTimeout(() => {
				uni.showModal({
					title: '境界突破',
					content: realm_breakthrough.message,
					showCancel: false,
					confirmText: '确定'
				});
			}, 500);
		}

		// 彻底去除奖励弹窗逻辑
		// if (rewards && Object.keys(rewards).length > 0) {
		// 	setTimeout(() => {
		// 		let rewardText = '获得奖励：\n'
		// 		for (const [key, value] of Object.entries(rewards)) {
		// 			rewardText += `${key}: ${value}\n`
		// 		}
		// 	uni.showModal({
		// 		title: '获得奖励',
		// 		content: rewardText,
		// 		showCancel: false,
		// 		confirmText: '确定'
		// 	})
		// }, 500)
		// }
	}
	
	/**
	 * 获取事件类型名称
	 */
	getEventTypeName(eventType) {
		const eventTypeMap = {
			1: '好运事件',
			2: '遭遇NPC',
			3: '采集事件',
			4: '普通事件',
			5: '奇遇事件',
			6: '恩怨事件',
			7: '组队事件',
			8: '商队事件',
			9: '江湖传闻',
			10: '天气事件',
			11: '神秘事件',
			12: '节日事件'
		};
		
		return eventTypeMap[eventType] || '江湖事件';
	}

	/**
	 * 注册状态更新回调
	 */
	onUpdate(callback) {
		this.updateCallbacks.push(callback)
	}

	/**
	 * 移除状态更新回调
	 */
	offUpdate(callback) {
		const index = this.updateCallbacks.indexOf(callback)
		if (index > -1) {
			this.updateCallbacks.splice(index, 1)
		}
	}

	/**
	 * 通知状态更新
	 */
	notifyUpdate(type) {
		this.updateCallbacks.forEach((callback, index) => {
			try {
				callback(type, this)
			} catch (error) {
				// 状态更新回调执行失败
			}
		})
	}

	/**
	 * 初始化游戏状态
	 */
	async init() {
		try {
			// 连接WebSocket
			await wsManager.connect();
			
			// 认证消息会在WebSocket连接成功后自动发送
			// 不需要在这里重复发送
			
		} catch (error) {
			console.error('游戏初始化失败:', error)
			uni.showToast({
				title: '连接服务器失败',
				icon: 'none'
			})
		}
	}

	/**
	 * 请求所有游戏数据
	 */
	requestAllData() {
		// 确保WebSocket连接已建立
		if (!wsManager.isConnected) {
			wsManager.connect().then(() => {
				this.sendDataRequests();
			}).catch(error => {
				console.error('[gameState] WebSocket连接失败:', error);
			});
		} else {
			this.sendDataRequests();
		}
	}
	
	/**
	 * 发送所有数据请求
	 */
	sendDataRequests() {
		// 请求玩家数据
		wsManager.sendMessage('get_player_data');

		// 请求背包数据
		setTimeout(() => {
			wsManager.sendMessage('get_inventory_data');
		}, 200);
		
		// 请求装备数据 - 使用特殊处理方式
		setTimeout(() => {
			// 使用gameUtils.sendMessage而不是直接使用wsManager.sendMessage
			// 这样可以利用gameUtils.sendMessage中对get_equipment_data的特殊处理
			const { gameUtils } = require('./gameData.js');
			if (gameUtils && gameUtils.sendMessage) {
				gameUtils.sendMessage({
					type: 'get_equipment_data',
					data: {}
				}).then(response => {
					// 手动触发装备数据处理
					if (response.type === 'equipment_data' && response.data) {
						this.equipment = response.data;
						this.notifyUpdate('equipment');
					}
				}).catch(error => {
					console.error('[gameState] 获取装备数据失败:', error);
				});
			} else {
				wsManager.sendMessage('get_player_data');
			}
		}, 400);
		
		// 请求武功数据
		setTimeout(() => {
			wsManager.sendMessage('get_skills_data');
		}, 600);

		// 请求地图配置
		setTimeout(() => {
			this.requestMapsConfig();
		}, 800);

		// 请求物品配置
		setTimeout(() => {
			this.requestItemsConfig();
		}, 1000);
	}

	/**
	 * 闯江湖
	 */
	triggerAdventure() {
		// 优先使用WebSocket管理器的认证状态
		const isAuthenticated = wsManager.isAuthed || this.isAuthed;

		if (!isAuthenticated) {
			uni.showToast({ title: '请先登录', icon: 'none' })
			return
		}

		if (!wsManager.isConnected) {
			uni.showToast({ title: '网络连接失败', icon: 'none' })
			return
		}

		wsManager.sendAdventureRequest()
	}

	/**
	 * 打坐
	 */
	meditate() {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendMeditationRequest();
	}

	/**
	 * 疗伤
	 */
	heal() {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendHealingRequest();
	}

	/**
	 * 装备操作
	 */
	equipItem(itemId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendEquipmentAction('equip', itemId);
	}

	unequipItem(slot) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendEquipmentAction('unequip', slot);
	}

	/**
	 * 武功操作
	 */
	learnSkill(skillId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendSkillAction('learn', skillId);
	}

	practiceSkill(skillId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendSkillAction('practice', skillId);
	}

	/**
	 * 商店操作
	 */
	buyItem(itemId, quantity = 1, npcId = '') {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		const data = { item_id: itemId, quantity };
		if (npcId) data.npc_id = npcId;
		wsManager.sendShopAction('buy', data);
	}

	sellItem(itemId, quantity = 1) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendShopAction('sell', { itemId, quantity });
	}

	/**
	 * 市场操作
	 */
	listItem(itemId, price, quantity = 1) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendMarketAction('list', { itemId, price, quantity });
	}

	buyMarketItem(itemId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendMarketAction('buy', { itemId });
	}

	cancelListing(itemId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendMarketAction('cancel', { itemId });
	}

	/**
	 * 门派操作
	 */
	joinGuild(guildId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendGuildAction('join', { guildId });
	}

	acceptGuildTask(taskId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendGuildAction('accept_task', { taskId });
	}

	learnGuildSkill(skillId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendGuildAction('learn_skill', { skillId });
	}

	/**
	 * 打造操作
	 */
	craftItem(recipeId) {
		if (!this.isAuthed) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			return;
		}
		wsManager.sendCraftingAction('craft', { recipeId });
	}

	/**
	 * 获取玩家数据
	 */
	getPlayer() {
		return this.player
	}

	/**
	 * 获取背包数据
	 */
	getInventory() {
		return this.inventory
	}

	/**
	 * 获取装备数据
	 */
	getEquipment() {
		return this.equipment
	}

	/**
	 * 获取武功数据
	 */
	getSkills() {
		return this.skills
	}

	/**
	 * 获取事件日志
	 */
	getEventLog() {
		return this.eventLog
	}

	/**
	 * 获取货币数据
	 */
	getCurrency() {
		return {
			silver: this.money,
			gold: this.gold
		}
	}

	/**
	 * 获取状态
	 */
	getStatus() {
		return this.status
	}

	/**
	 * 断开连接
	 */
	disconnect() {
		wsManager.disconnect()
	}

	getPlayerData() {
		return this.player;
	}

	/**
	 * 设置玩家数据并通知更新
	 */
	setPlayerData(data) {
		this.player = data;
		this.notifyUpdate('player');
	}

	updateMoney() {
		// 优先从player对象获取最新银两
		if (this.player && (typeof this.player.money === 'number')) {
			this.money = this.player.money
		} else if (this.money) {
			this.money = this.money
		}
	}

	updateData() {
		this.myItems = [...this.inventory]
	}

	/**
	 * 新增：请求物品配置（结构化JSON）
	 */
	requestItemsConfig() {
		return new Promise((resolve, reject) => {
			wsManager.sendMessage('get_items_config', {})
			wsManager.on('items_config', (data) => {
				this.itemsConfig = data || {}
				this.notifyUpdate('itemsConfig')
				resolve(this.itemsConfig)
			})
			// 可加超时处理
		})
	}

	/**
	 * 获取物品配置（如未加载则自动请求）
	 */
	async getItemsConfig() {
		if (Object.keys(this.itemsConfig).length === 0) {
			await this.requestItemsConfig()
		}
		return this.itemsConfig
	}

	/**
	 * 新增：请求地图配置（结构化JSON）
	 */
	requestMapsConfig() {
		return new Promise((resolve, reject) => {
			wsManager.sendMessage('get_maps_config', {})
			wsManager.on('maps_config', (response) => {
				this.mapsConfig = {};
				// 后端返回格式：{'type': 'maps_config', 'data': ...}
				const data = response.data || response;
				if (Array.isArray(data)) {
					for (const map of data) {
						if (map.id) {
							this.mapsConfig[map.id] = map;
						}
					}
				} else if (typeof data === 'object' && data !== null) {
					// 兼容对象格式
					for (const [id, map] of Object.entries(data)) {
						if (map && map.id) {
							this.mapsConfig[map.id] = map;
						}
					}
				}
				this.notifyUpdate('mapsConfig')
				resolve(this.mapsConfig)
			})
			// 可加超时处理
		})
	}

	/**
	 * 获取地图配置（如未加载则自动请求）
	 */
	async getMapsConfig() {
		if (Object.keys(this.mapsConfig).length === 0) {
			await this.requestMapsConfig()
		}
		return this.mapsConfig
	}
}

// 创建单例实例
const gameState = new GameStateManager()

export default gameState 